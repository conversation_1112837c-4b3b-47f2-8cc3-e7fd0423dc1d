package net.dev.mod.block;

import java.util.function.Function;

import net.dev.mod.ServerMod;
import net.fabricmc.fabric.api.itemgroup.v1.ItemGroupEvents;
import net.minecraft.block.AbstractBlock;
import net.minecraft.block.Block;
import net.minecraft.block.Blocks;
import net.minecraft.block.PillarBlock;
import net.minecraft.item.BlockItem;
import net.minecraft.item.Item;
import net.minecraft.item.ItemGroups;
import net.minecraft.registry.Registries;
import net.minecraft.registry.Registry;
import net.minecraft.registry.RegistryKey;
import net.minecraft.registry.RegistryKeys;
import net.minecraft.util.Identifier;
public class ModBlocks {

	public static final Block CYPRESS_LOG = register("cypress_log", PillarBlock::new, 
		AbstractBlock.Settings.copy(Blocks.OAK_LOG),
	 	true, new Item.Settings());
	public static final Block CYPRESS_WOOD = register("cypress_wood", PillarBlock::new, 
		AbstractBlock.Settings.copy(Blocks.OAK_WOOD),
	 	true, new Item.Settings());
	public static final Block STRIPPED_CYPRESS_LOG = register("stripped_cypress_log", PillarBlock::new, 
		AbstractBlock.Settings.copy(Blocks.STRIPPED_OAK_LOG),
	 	true, new Item.Settings());
	public static final Block STRIPPED_CYPRESS_WOOD = register("stripped_cypress_log", PillarBlock::new, 
		AbstractBlock.Settings.copy(Blocks.STRIPPED_OAK_WOOD),
	 	true, new Item.Settings());
	

    private static Block register(String name, Function<AbstractBlock.Settings, Block> blockFactory, AbstractBlock.Settings settings, boolean shouldRegisterItem, Item.Settings itemSettings) {
		// Create a registry key for the block
		RegistryKey<Block> blockKey = keyOfBlock(name);
		// Create the block instance
		Block block = blockFactory.apply(settings.registryKey(blockKey));

		// Sometimes, you may not want to register an item for the block.
		// Eg: if it's a technical block like `minecraft:moving_piston` or `minecraft:end_gateway`
		if (shouldRegisterItem) {
			// Items need to be registered with a different type of registry key, but the ID
			// can be the same.
			RegistryKey<Item> itemKey = keyOfItem(name);

			BlockItem blockItem = new BlockItem(block, itemSettings.registryKey(itemKey));
			Registry.register(Registries.ITEM, itemKey, blockItem);
		}

		return Registry.register(Registries.BLOCK, blockKey, block);
	}
    private static RegistryKey<Block> keyOfBlock(String name) {
		return RegistryKey.of(RegistryKeys.BLOCK, Identifier.of(ServerMod.MOD_ID, name));
	}

	private static RegistryKey<Item> keyOfItem(String name) {
		return RegistryKey.of(RegistryKeys.ITEM, Identifier.of(ServerMod.MOD_ID, name));
	}
	public static void registerModBlocks() {
        ServerMod.LOGGER.info("Registering Mod Blocks for " + ServerMod.MOD_ID);
		ItemGroupEvents.modifyEntriesEvent(ItemGroups.NATURAL).register(entries -> {
            entries.add(ModBlocks.CYPRESS_LOG);
            entries.add(ModBlocks.CYPRESS_WOOD);
            entries.add(ModBlocks.STRIPPED_CYPRESS_LOG);
            entries.add(ModBlocks.STRIPPED_CYPRESS_WOOD);
        });
    }
}
