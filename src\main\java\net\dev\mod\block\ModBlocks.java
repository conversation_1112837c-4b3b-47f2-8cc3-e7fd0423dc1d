package net.dev.mod.block;

import java.util.Optional;
import java.util.function.Function;

import net.dev.mod.ServerMod;
import net.fabricmc.fabric.api.itemgroup.v1.ItemGroupEvents;
import net.minecraft.block.AbstractBlock;
import net.minecraft.block.Block;
import net.minecraft.block.Blocks;
import net.minecraft.block.PillarBlock;
import net.minecraft.block.SaplingBlock;
import net.minecraft.block.SaplingGenerator;
import net.minecraft.item.BlockItem;
import net.minecraft.item.Item;
import net.minecraft.item.ItemGroups;
import net.minecraft.registry.Registries;
import net.minecraft.registry.Registry;
import net.minecraft.registry.RegistryKey;
import net.minecraft.registry.RegistryKeys;
import net.minecraft.util.Identifier;
public class ModBlocks {

	public static final Block CYPRESS_LOG = register("cypress_log", PillarBlock::new, 
		AbstractBlock.Settings.copy(Blocks.OAK_LOG),
	 	true, new Item.Settings());
	public static final Block CYPRESS_WOOD = register("cypress_wood", PillarBlock::new, 
		AbstractBlock.Settings.copy(Blocks.OAK_WOOD),
	 	true, new Item.Settings());
	public static final Block STRIPPED_CYPRESS_LOG = register("stripped_cypress_log", PillarBlock::new, 
		AbstractBlock.Settings.copy(Blocks.STRIPPED_OAK_LOG),
	 	true, new Item.Settings());
	public static final Block STRIPPED_CYPRESS_WOOD = register("stripped_cypress_wood", PillarBlock::new,
		AbstractBlock.Settings.copy(Blocks.STRIPPED_OAK_WOOD),
	 	true, new Item.Settings());

	public static final Block CYPRESS_PLANKS = register("cypress_planks", Block::new,
		AbstractBlock.Settings.copy(Blocks.OAK_PLANKS),
	 	true, new Item.Settings());
	public static final Block CYPRESS_LEAVES = register("cypress_leaves", Leaves::new,
		AbstractBlock.Settings.copy(Blocks.OAK_LEAVES),
		true, new Item.Settings());

	public static final Block CYPRESS_SAPLING = registerSapling("cypress_sapling",
		AbstractBlock.Settings.copy(Blocks.OAK_SAPLING),
	 	true, new Item.Settings(), 
		new SaplingGenerator("alpine:cypress",
		Optional.empty(),
		Optional.of(RegistryKey.of(RegistryKeys.CONFIGURED_FEATURE, Identifier.of("alpine", "cypress/classic"))),
		Optional.empty()));

    private static Block register(String name, Function<AbstractBlock.Settings, Block> blockFactory, AbstractBlock.Settings settings, boolean shouldRegisterItem, Item.Settings itemSettings) {
		// Create a registry key for the block
		RegistryKey<Block> blockKey = keyOfBlock(name);
		// Create the block instance
		Block block = blockFactory.apply(settings.registryKey(blockKey));

		if (shouldRegisterItem) {
			RegistryKey<Item> itemKey = keyOfItem(name);

			BlockItem blockItem = new BlockItem(block, itemSettings.registryKey(itemKey));
			Registry.register(Registries.ITEM, itemKey, blockItem);
		}

		return Registry.register(Registries.BLOCK, blockKey, block);
	}
	private static Block registerSapling(String name, AbstractBlock.Settings settings, boolean shouldRegisterItem, Item.Settings itemSettings, SaplingGenerator generator) {
		// Create a registry key for the block
		RegistryKey<Block> blockKey = keyOfBlock(name);
		// Create the block instance
		SaplingBlock block = new SaplingBlock(generator, settings.registryKey(blockKey));

		if (shouldRegisterItem) {
			RegistryKey<Item> itemKey = keyOfItem(name);

			BlockItem blockItem = new BlockItem(block, itemSettings.registryKey(itemKey));
			Registry.register(Registries.ITEM, itemKey, blockItem);
		}

		return Registry.register(Registries.BLOCK, blockKey, block);
	}
    private static RegistryKey<Block> keyOfBlock(String name) {
		return RegistryKey.of(RegistryKeys.BLOCK, Identifier.of(ServerMod.MOD_ID, name));
	}

	private static RegistryKey<Item> keyOfItem(String name) {
		return RegistryKey.of(RegistryKeys.ITEM, Identifier.of(ServerMod.MOD_ID, name));
	}
	public static void registerModBlocks() {
        ServerMod.LOGGER.info("Registering Mod Blocks for " + ServerMod.MOD_ID);
		ItemGroupEvents.modifyEntriesEvent(ItemGroups.NATURAL).register(entries -> {
            entries.add(ModBlocks.CYPRESS_LOG);
            entries.add(ModBlocks.CYPRESS_WOOD);
            entries.add(ModBlocks.CYPRESS_LEAVES);
			entries.add(ModBlocks.CYPRESS_SAPLING);
        });
		ItemGroupEvents.modifyEntriesEvent(ItemGroups.BUILDING_BLOCKS).register(entries -> {
            entries.add(ModBlocks.CYPRESS_LOG);
            entries.add(ModBlocks.CYPRESS_WOOD);
            entries.add(ModBlocks.STRIPPED_CYPRESS_LOG);
            entries.add(ModBlocks.STRIPPED_CYPRESS_WOOD);
            entries.add(ModBlocks.CYPRESS_PLANKS);
        });
    }
}
