{"schemaVersion": 1, "id": "mod", "version": "${version}", "name": "Server Mod", "description": "This is an example description! Tell everyone what your mod is about!", "authors": ["Me!"], "contact": {"homepage": "", "sources": ""}, "license": "CC0-1.0", "icon": "assets/mod/icon.png", "environment": "*", "entrypoints": {"main": ["net.dev.mod.ServerMod"], "client": ["net.dev.mod.ServerModClient"], "fabric-datagen": ["net.dev.mod.ServerModDataGenerator"]}, "mixins": ["mod.mixins.json", {"config": "mod.client.mixins.json", "environment": "client"}], "depends": {"fabricloader": ">=0.16.14", "minecraft": ">=1.21.5", "java": ">=21", "fabric-api": "*"}, "suggests": {"another-mod": "*"}}