import json
#for generating data
#--------------------------------------------------


namespace = "mod"


#----------------------------------------------------
#string generation
bim1 = namespace.join(["""{
    "parent": "minecraft:item/generated",
    "textures": {
        "layer0": \"""",""":item/"""])
ubm1 = namespace.join(["""{
    "parent": "minecraft:block/cube_all",
    "textures": {
        "all": \"""",""":block/"""])
bbm1 = namespace.join(["""{
    "parent": "minecraft:block/cube",
    "textures": {
        "down": \"""",""":block/"""])
bbm2 = namespace.join(["""_down",
        "east": \"""",""":block/"""])
bbm3 = namespace.join(["""_east",
        "north": \"""",""":block/"""])
bbm4 = namespace.join(["""_north",
        "particle": \"""",""":block/"""])
bbm5 = namespace.join(["""_particle",
        "south": \"""",""":block/"""])
bbm6 = namespace.join(["""_south",
        "up": \"""",""":block/"""])
bbm7 = namespace.join(["""_up",
        "west": \"""",""":block/"""])
bki1 = namespace.join(["""{
    "parent": \"""",""":block/"""])
bbs1 = namespace.join(["""{
    "variants": {
        "": {
            "model": \"""",""":block/"""])
loot1 = namespace.join(["""{
  "type": "block",
  "pools": [
    {
      "rolls": 1,
      "bonus_rolls": 0,
      "entries": [
        {
          "type": "item",
          "weight": 1,
          "name": \"""", ":"])
log1 = namespace.join(["""{
  "parent": "minecraft:block/cube_column",
  "textures": {
    "end": \"""", ":block/"])
log2 = namespace.join(["""_top",
    "side": \"""",""":block/"""])
pbm1 = namespace.join(["""{
  "parent": "minecraft:block/cube_column",
  "textures": {
    "end": \"""", ":block/"""])
pbm2 = namespace.join(["""_top",
    "side": \"""",""":block/"""])
#pillar block state
pbs1 = namespace.join(["""{
    "variants": {
        "axis=x": {
            "x": 90,
            "y": 90,
            "model": \"""",""":block/"""])
pbs2 = namespace.join(["""\"
        },
        "axis=y": {
            "model": \"""",""":block/"""])
pbs3 = namespace.join(["""\"
        },
        "axis=z": {
            "x": 90,
            "model": \"""",""":block/"""])
item1 = namespace.join(["""{
    "model": {
        "type":"minecraft:model",
        "model":\"""",""":item/""" ])
#----------------------------------------------------
def item(name):
    with open("./src/client/resources/assets/{}/items/{}.json".format(namespace,name), "w") as f:
        f.write(name.join([item1, """\"
    },
    "properties": {
        "hand_animation_on_swap": false
    }
}"""]))
        f.close()
def BIM():
    name = input("Name: ")
    with open("./src/client/resources/assets/{}/models/item/{}.json".format(namespace,name), "w") as f:
        f.write(name.join([bim1, """"
    }
}"""]))
        f.close()
    item(name)
def UBM():
    name = input("Name: ")
    with open("./src/client/resources/assets/{}/models/block/{}.json".format(namespace,name), "w") as f:
        f.write(name.join([ubm1, """"
    }
}"""]))
        f.close()
    want = input("Block Item Model? (y/n)").upper()
    if want == "Y":
        BKI(name)
    want = input("Blockstate file? (y/n)").upper()
    if want == "Y":
        BBS(name)
    
def BBM():
    name = input("Name: ")
    with open("./src/client/resources/assets/{}/models/block/{}.json".format(namespace,name), "w") as f:
        f.write(name.join([bbm1, bbm2, bbm3, bbm4, bbm5, bbm6, bbm7,"""_west"
    }
}"""]))
        f.close()
    want = input("Block Item Model? (y/n)").upper()
    if want == "Y":
        BKI(name)
    want = input("Blockstate file? (y/n)").upper()
    if want == "Y":
        BBS(name)
def BKI(name=None):
    if name == None:
        name = input("Name: ")
    with open("./src/client/resources/assets/{}/models/item/{}.json".format(namespace,name), "w")as f:
        f.write(name.join([bki1, """"
}"""]))
        f.close()
    item(name)
def BBS(name=None):
    if name == None:
        name = input("Name: ")
    with open("./src/client/resources/assets/{}/blockstates/{}.json".format(namespace,name), "w")as f:
        f.write(name.join([bbs1, """"
        }
    }
}"""]))
        f.close()
def PBS(name=None):
    if name == None:
        name = input("Name: ")
    with open("./src/client/resources/assets/{}/blockstates/{}.json".format(namespace,name), "w")as f:
        f.write(name.join([pbs1, pbs2, pbs3, """"
        }
    }
}"""]))
        f.close()
def BPB():
    name = input("Name: ")
    with open("./src/client/resources/assets/{}/models/block/{}.json".format(namespace,name), "w")as f:
        f.write(name.join([log1, log2, """"
    }
}"""]))
        f.close()
    want = input("Block Item Model? (y/n)").upper()
    if want == "Y":
        BKI(name)
def PBM():
    name = input("Name: ")
    with open("./src/client/resources/assets/{}/models/block/{}.json".format(namespace,name), "w")as f:
        f.write(name.join([pbm1, pbm2, """"
    }
}"""]))
        f.close()
    want = input("Blockstate file? (y/n)").upper()
    if want == "Y":
        PBS(name)
    want = input("Block Item Model? (y/n)").upper()
    if want == "Y":
        BKI(name)

def drop():
    name = input("Name: ")
    print("0: none")
    print("1:pickaxe")
    print("2:axe")
    print("3:shovel")
    print("4:shears")
    print("5:hoes")
    tool = input("Tool: ")
    if tool == "1":
        with open("./src/main/resources/data/minecraft/tags/block/mineable/pickaxe.json", "r")as f:
            current = json.loads(f.read())
            current["values"].append(":".join([namespace,name]))
            f.close()
        with open("./src/main/resources/data/minecraft/tags/block/mineable/pickaxe.json", "w")as f:
            f.write(json.dumps(current))
            f.close()
    elif tool == "2":
        with open("./src/main/resources/data/minecraft/tags/block/mineable/axe.json", "r")as f:
            current = json.loads(f.read())
            current["values"].append(":".join([namespace,name]))
            f.close()
        with open("./src/main/resources/data/minecraft/tags/block/mineable/axe.json", "w")as f:
            f.write(json.dumps(current))
            f.close()
    elif tool == "3":
        with open("./src/main/resources/data/minecraft/tags/block/mineable/shovel.json", "r")as f:
            current = json.loads(f.read())
            current["values"].append(":".join([namespace,name]))
            f.close()
        with open("./src/main/resources/data/minecraft/tags/block/mineable/shovel.json", "w")as f:
            f.write(json.dumps(current))
            f.close()
    print ("Required teir")
    print("0: none")
    print("1:wood")
    print("2:stone")
    print("3:iron")
    print("4:diamond")
    teir = input("Tool: ")
    if teir == "1":
        with open("./src/main/resources/data/minecraft/tags/block/needs_wood_tool.json", "r")as f:
            current = json.loads(f.read())
            current["values"].append(":".join([namespace,name]))
            f.close()
        with open("./src/main/resources/data/minecraft/tags/block/block/needs_wood_tool.json", "w")as f:
            f.write(json.dumps(current))
            f.close()
    elif teir == "2":
        with open("./src/main/resources/data/minecraft/tags/block/needs_stone_tool.json", "r")as f:
            current = json.loads(f.read())
            current["values"].append(":".join([namespace,name]))
            f.close()
        with open("./src/main/resources/data/minecraft/tags/block/needs_stone_tool.json", "w")as f:
            f.write(json.dumps(current))
            f.close()
    elif teir == "3":
        with open("./src/main/resources/data/minecraft/tags/block/needs_iron_tool.json", "r")as f:
            current = json.loads(f.read())
            current["values"].append(":".join([namespace,name]))
            f.close()
        with open("./src/main/resources/data/minecraft/tags/block/needs_iron_tool.json", "w")as f:
            f.write(json.dumps(current))
            f.close()
    elif teir == "4":
        with open("./src/main/resources/data/minecraft/tags/block/needs_diamond_tool.json", "r")as f:
            current = json.loads(f.read())
            current["values"].append(":".join([namespace,name]))
            f.close()
        with open("./src/main/resources/data/minecraft/tags/block/needs_diamond_tool.json", "w")as f:
            f.write(json.dumps(current))
            f.close()
    sel = input("drops self? (y/n)").upper()
    if sel == "Y":
        with open("./src/main/resources/data/{}/loot_table/blocks/{}.json".format(namespace,name), "w")as f:
            f.write(name.join([loot1, """"
        }
      ]
    }
  ]
}"""]))
            f.close()
    else:
        drops = input("drops single other item? (y/n)").upper()
        if drops == "Y":
            item = input("Item: ")
            with open("./src/main/resources/data/{}/loot_table/blocks/{}.json".format(namespace,name), "w")as f:
                f.write(item.join(["""{
  "type": "block",
  "pools": [
    {
      "rolls": 1,
      "bonus_rolls": 0,
      "entries": [
        {
          "type": "item",
          "weight": 1,
          "name": \"""", """"
        }
      ]
    }
  ]
}"""]))


#run loop
while True:
    command = input(">>> ").upper()
    if command == "":
        continue
    elif command == "BIM":
        BIM()
    elif command == "UBM":
        UBM()
    elif command == "BBM":
        BBM()
    elif command == "BKI":
        BKI()
    elif command == "BBS":
        BBS()
    elif command == "DROP":
        drop()
    elif command == "BPB":
        BPB()
    elif command == "PBM":
        PBM()
    elif command == "PBS":
        PBS()
    elif command == "EXIT":
        break
    else:
        print("unknown command")
    print()
