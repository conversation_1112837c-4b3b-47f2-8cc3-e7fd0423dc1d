package net.dev.mod.mixin;

import net.minecraft.block.MapColor;
import net.minecraft.util.DyeColor;
import org.spongepowered.asm.mixin.Final;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Mutable;
import org.spongepowered.asm.mixin.Shadow;
import org.spongepowered.asm.mixin.gen.Invoker;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Objects;

@Mixin(DyeColor.class)
public class DyeColorMixin {
    @Invoker("<init>")
    public static DyeColor callInit(String name, int id, int index, String idi, int entityColor, MapColor mapColor, int fireworkColor, int signColor) {
        throw new AssertionError();
    }

    @Shadow
    @Final
    @Mutable
    private static DyeColor[] field_1234;

    /**
     * Adds a new DyeColor at runtime. Thread-safe and duplicate-safe.
     * Should be called during mod initialization.
     *
     * @return The new DyeColor instance, or the existing one if duplicate.
     */
    public static synchronized DyeColor add(String name, int id, int index, String idi, int entityColor, MapColor mapColor, int fireworkColor, int signColor) {
        // Check for duplicate by name or ordinal
        for (DyeColor color : field_1234) {
            if (color.name().equalsIgnoreCase(name) || color.ordinal() == index) {
                return color;
            }
        }
        ArrayList<DyeColor> list = new ArrayList<>(Arrays.asList(field_1234));
        DyeColor color = callInit(name, id, index, idi, entityColor, mapColor, fireworkColor, signColor);
        list.add(color);
        field_1234 = list.toArray(new DyeColor[0]);
        return color;
    }
}