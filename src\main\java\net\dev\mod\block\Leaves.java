package net.dev.mod.block;

import com.mojang.serialization.MapCodec;
import com.mojang.serialization.codecs.RecordCodecBuilder;

import net.minecraft.block.AbstractBlock;
import net.minecraft.block.LeavesBlock;
import net.minecraft.particle.EntityEffectParticleEffect;
import net.minecraft.particle.ParticleTypes;
import net.minecraft.particle.ParticleUtil;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.random.Random;
import net.minecraft.world.World;

public class Leaves extends LeavesBlock{
    public static final MapCodec<Leaves> CODEC = RecordCodecBuilder.mapCodec(
		instance -> instance.group(
				createSettingsCodec()
			)
			.apply(instance, Leaves::new)
	);

	public Leaves(AbstractBlock.Settings settings) {
		super(0.01F, settings);
	}

	@Override
	protected void spawnLeafParticle(World world, BlockPos pos, Random random) {
		EntityEffectParticleEffect entityEffectParticleEffect = EntityEffectParticleEffect.create(ParticleTypes.TINTED_LEAVES, world.getBlockColor(pos));
		ParticleUtil.spawnParticle(world, pos, random, entityEffectParticleEffect);
	}

	@Override
	public MapCodec<? extends Leaves> getCodec() {
		return CODEC;
	}

}
